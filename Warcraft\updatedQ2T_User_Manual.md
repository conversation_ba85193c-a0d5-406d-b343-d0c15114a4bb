# Quest to Talk - Voice Automation User Manual

## Overview
Quest to Talk is an automation tool designed to help World of Warcraft players convert quest text to speech. The program captures quest text from your screen, processes it, and sends it to a text-to-speech application for voice narration.

## Prerequisites
- Python 3.x installed
- CustomTkinter library (`pip install customtkinter`)
- pytesseract library (`pip install pytesseract`)
- Tesseract OCR software installed on your system
- A text-to-speech application (like Discord, etc.)
- The reference image file for the "read aloud" button

## How to Start the Program

1. **Launch the Application**
   - Run the `updatedQ2T.py` file with Python
   - A modern GUI window will appear with the title "Quest to Talk - Voice Automation"

2. **GUI Interface**
   The interface contains:
   - **Status Label**: Shows the current program status
   - **Start Button**: Begins the automation listener
   - **Stop Button**: Stops the automation listener
   - **Quit Button**: Closes the program completely

## Using the Program

### Step 1: Setup
1. Make sure World of Warcraft is running
2. Have your text-to-speech application open (Discord, etc.)
3. Ensure the quest text is visible on your screen in the designated region

### Step 2: Starting the Automation
1. Click the **Start** button in the GUI
2. The status will change to "Starting..." then "Running (Waiting for AltGr)"
3. The Start button becomes disabled, Stop button becomes enabled

### Step 3: Triggering the Automation
The program listens for two key combinations:

- **Alt Gr**: Standard voice processing
- **Shift + Alt Gr**: Voice processing with voice tab switching

When you press these keys while in-game:

1. **Text Capture**: The program takes a screenshot of the quest text region
2. **OCR Processing**: Converts the image to text using Tesseract OCR
3. **Text Filtering**: Removes unwanted elements (objectives, currency, etc.)
4. **Application Switch**: Switches to your text-to-speech application
5. **Text Input**: Pastes the filtered text into the chat box
6. **Voice Activation**: Finds and clicks the "read aloud" button
7. **Return to Game**: Switches back to World of Warcraft

### Step 4: Stopping the Program
- Click the **Stop** button to halt the automation
- The status changes to "Stopped"
- Start button becomes enabled again

## Key Features

### Automatic Text Processing
- Captures NPC names and quest dialogue
- Removes objectives, rewards, and currency information
- Filters out quest IDs and other metadata

### Smart Application Switching
- Uses Alt+Tab to switch between applications
- Handles voice tab switching with Shift modifier
- Returns focus to the game after processing

### Error Handling
- Continues running even if OCR fails
- Provides status updates for troubleshooting
- Graceful handling of missing files or dependencies

### Visual Feedback
- Real-time status updates in the GUI
- Console output for detailed operation logs
- Button state changes to reflect program status

## Troubleshooting

### Common Issues

**"Error: Tesseract path invalid"**
- Install Tesseract OCR from UB Mannheim
- Ensure it's added to your system PATH
- Restart the program after installation

**"Error: Reference image missing"**
- Check that the reference image file exists at the specified path
- Update the REFERENCE_IMAGE_PATH in the script if needed

**"OCR Failed"**
- Verify quest text is clearly visible on screen
- Check that the TEXT_REGION coordinates are correct
- Ensure good contrast between text and background

**Program doesn't respond to Alt Gr**
- Make sure the program is running (status shows "Running")
- Try pressing Alt Gr while the game window is active
- Check if your keyboard has an Alt Gr key (some US keyboards don't)

### Configuration

You can modify these settings in the script:
- `TEXT_REGION`: Screen coordinates for quest text capture
- `CHATBOX_COORDS`: Click coordinates for the chat input box
- `REFERENCE_IMAGE_PATH`: Path to the "read aloud" button image
- `IMAGE_CONFIDENCE`: Confidence level for image matching

## Safety Notes

- The program uses global key listeners - it will respond to Alt Gr from any application
- Always stop the program before closing to ensure proper cleanup
- The program suppresses Alt Gr key presses to prevent unwanted characters
- Test the coordinates and image paths before using in important gameplay

## Tips for Best Results

1. **Screen Setup**: Position quest text in a consistent location
2. **Lighting**: Ensure good contrast for better OCR accuracy
3. **Text Size**: Larger text generally works better for OCR
4. **Stable Setup**: Keep window positions consistent between sessions
5. **Testing**: Test the automation in a safe environment first

## Keyboard Shortcuts Summary

- **Alt Gr**: Trigger standard voice automation
- **Shift + Alt Gr**: Trigger voice automation with tab switching
- **GUI Buttons**: Use Start/Stop buttons for program control

---

*Note: This program is designed for accessibility and convenience. Always ensure you're following the terms of service for any games or applications you use it with.*
