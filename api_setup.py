"""
Google Gemini API Setup and Examples

This file contains the setup instructions and example code for using the Google Gemini API.

Setup Instructions:
1. Get a free API key from Google AI Studio: https://aistudio.google.com/
2. Install the Google GenAI SDK using pip
3. Set your API key as an environment variable GEMINI_API_KEY
4. Run the example code below

Installation Command:
pip install -q -U google-genai
"""

import os
from google import genai
from google.genai import types

# Your API key
API_KEY = "AIzaSyBr2ObEMt7htXiyIsagTLZddxePjOz9gcc"

def setup_gemini_client():
    """
    Initialize the Gemini client with your API key.
    """
    try:
        # Set the API key in the environment if not already set
        if not os.getenv('GEMINI_API_KEY'):
            os.environ['GEMINI_API_KEY'] = API_KEY

        client = genai.Client()
        return client
    except Exception as e:
        print(f"Error initializing Gemini client: {e}")
        return None

def basic_example():
    """
    Basic example using the generateContent method with Gemini 2.5 Flash model.
    """
    client = setup_gemini_client()
    if not client:
        return
    
    try:
        response = client.models.generate_content(
            model="gemini-2.5-flash", 
            contents="Explain how AI works in a few words"
        )
        print("Basic Example Response:")
        print(response.text)
        print("-" * 50)
    except Exception as e:
        print(f"Error in basic example: {e}")

def example_with_thinking_disabled():
    """
    Example with thinking feature disabled for faster response and lower token usage.
    """
    client = setup_gemini_client()
    if not client:
        return
    
    try:
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="Explain how AI works in a few words",
            config=types.GenerateContentConfig(
                thinking_config=types.ThinkingConfig(thinking_budget=0)  # Disables thinking
            ),
        )
        print("Example with Thinking Disabled:")
        print(response.text)
        print("-" * 50)
    except Exception as e:
        print(f"Error in thinking disabled example: {e}")

def check_api_key():
    """
    Check if the API key is available.
    """
    # Set the API key if not already in environment
    if not os.getenv('GEMINI_API_KEY'):
        os.environ['GEMINI_API_KEY'] = API_KEY

    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        print("✓ API key is configured and ready to use")
        print(f"✓ Using API key: {api_key[:10]}...{api_key[-4:]}")  # Show partial key for verification
        return True
    else:
        print("✗ API key is not available")
        return False

def main():
    """
    Main function to run the examples.
    """
    print("Google Gemini API Setup and Examples")
    print("=" * 40)
    
    # Check if API key is set
    if not check_api_key():
        return
    
    print("\nRunning examples...")
    print()
    
    # Run basic example
    basic_example()
    
    # Run example with thinking disabled
    example_with_thinking_disabled()
    
    print("Setup complete! You can now use the Gemini API in your projects.")

if __name__ == "__main__":
    main()
