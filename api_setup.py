"""
Google Gemini API Setup and Examples

This file contains the setup instructions and example code for using the Google Gemini API.

Setup Instructions:
1. Get a free API key from Google AI Studio: https://aistudio.google.com/
2. Install the Google GenAI SDK using pip
3. Set your API key as an environment variable GEMINI_API_KEY
4. Run the example code below

Installation Command:
pip install -q -U google-genai
"""

import os
from google import genai
from google.genai import types

def setup_gemini_client():
    """
    Initialize the Gemini client.
    The client gets the API key from the environment variable `GEMINI_API_KEY`.
    """
    try:
        client = genai.Client()
        return client
    except Exception as e:
        print(f"Error initializing Gemini client: {e}")
        print("Make sure you have set the GEMINI_API_KEY environment variable")
        return None

def basic_example():
    """
    Basic example using the generateContent method with Gemini 2.5 Flash model.
    """
    client = setup_gemini_client()
    if not client:
        return
    
    try:
        response = client.models.generate_content(
            model="gemini-2.5-flash", 
            contents="Explain how AI works in a few words"
        )
        print("Basic Example Response:")
        print(response.text)
        print("-" * 50)
    except Exception as e:
        print(f"Error in basic example: {e}")

def example_with_thinking_disabled():
    """
    Example with thinking feature disabled for faster response and lower token usage.
    """
    client = setup_gemini_client()
    if not client:
        return
    
    try:
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="Explain how AI works in a few words",
            config=types.GenerateContentConfig(
                thinking_config=types.ThinkingConfig(thinking_budget=0)  # Disables thinking
            ),
        )
        print("Example with Thinking Disabled:")
        print(response.text)
        print("-" * 50)
    except Exception as e:
        print(f"Error in thinking disabled example: {e}")

def check_api_key():
    """
    Check if the API key is set as an environment variable.
    """
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        print("✓ GEMINI_API_KEY environment variable is set")
        return True
    else:
        print("✗ GEMINI_API_KEY environment variable is not set")
        print("Please set your API key as an environment variable:")
        print("Windows: set GEMINI_API_KEY=your_api_key_here")
        print("Linux/Mac: export GEMINI_API_KEY=your_api_key_here")
        return False

def main():
    """
    Main function to run the examples.
    """
    print("Google Gemini API Setup and Examples")
    print("=" * 40)
    
    # Check if API key is set
    if not check_api_key():
        return
    
    print("\nRunning examples...")
    print()
    
    # Run basic example
    basic_example()
    
    # Run example with thinking disabled
    example_with_thinking_disabled()
    
    print("Setup complete! You can now use the Gemini API in your projects.")

if __name__ == "__main__":
    main()
