# Quest to Talk - Executable Build Instructions

This folder contains scripts to package your Quest to Talk Python script into a standalone executable (.exe) file.

## Prerequisites

1. **Python 3.7+** installed on your system
2. **Tesseract OCR** installed (for the final executable to work)
3. All required Python packages (will be installed automatically)

## Build Methods

### Method 1: Simple Build (Recommended)
1. Double-click `build_exe.bat`
2. Wait for the build process to complete
3. Find your executable in the `dist` folder

### Method 2: Advanced Build
1. Run `python build_exe_advanced.py`
2. This provides more detailed output and error handling

### Method 3: Manual Build
1. Install dependencies: `pip install -r requirements.txt`
2. Run PyInstaller: `pyinstaller build_exe.spec --clean --noconfirm`

## Output

- **Executable**: `dist/QuestToTalk.exe`
- **Size**: Approximately 50-100 MB (includes all dependencies)
- **Type**: Standalone executable (no Python installation required on target machine)

## Important Notes

### For Distribution:
1. **Tesseract OCR** must be installed on the target machine
2. The reference image path in the script may need to be updated
3. Test the executable thoroughly before distributing

### Troubleshooting:
- If build fails, check that all dependencies are installed
- Ensure the Python script path is correct in the spec file
- Check that the reference image path exists (or comment it out)

### Customization:
- Edit `build_exe.spec` to modify build settings
- Change `console=True` to `console=False` to hide the console window
- Add an icon by setting `icon='path/to/icon.ico'`

## File Structure After Build

```
├── dist/
│   └── QuestToTalk.exe          # Your executable
├── build/                       # Temporary build files (can be deleted)
├── QuestToTalk.spec            # PyInstaller specification
├── requirements.txt            # Python dependencies
└── build_exe.bat              # Build script
```

## Testing the Executable

1. Copy `QuestToTalk.exe` to a different folder
2. Ensure Tesseract is installed on the test machine
3. Update the reference image path if needed
4. Run the executable and test all functionality

## Distribution

The `QuestToTalk.exe` file is completely standalone and can be distributed without requiring Python installation on the target machine. However, Tesseract OCR must still be installed separately.
