#!/usr/bin/env python3
"""
Ethernet 2 Toggle Script
Creates a desktop shortcut to enable/disable Ethernet 2 network adapter
"""

import subprocess
import sys
import os
import tkinter as tk
from tkinter import messagebox
import winshell
from win32com.client import Dispatch

def get_adapter_status(adapter_name="Ethernet 2"):
    """Check if the network adapter is enabled or disabled"""
    try:
        # Use netsh to get adapter status
        result = subprocess.run(
            ['netsh', 'interface', 'show', 'interface', adapter_name],
            capture_output=True, text=True, shell=True
        )
        
        if "Enabled" in result.stdout:
            return True
        elif "Disabled" in result.stdout:
            return False
        else:
            return None
    except Exception as e:
        print(f"Error checking adapter status: {e}")
        return None

def toggle_adapter(adapter_name="Ethernet 2"):
    """Toggle the network adapter on/off"""
    try:
        # Check current status
        is_enabled = get_adapter_status(adapter_name)
        
        if is_enabled is None:
            return False, "Could not determine adapter status"
        
        if is_enabled:
            # Disable the adapter
            result = subprocess.run(
                ['netsh', 'interface', 'set', 'interface', adapter_name, 'disable'],
                capture_output=True, text=True, shell=True
            )
            action = "disabled"
        else:
            # Enable the adapter
            result = subprocess.run(
                ['netsh', 'interface', 'set', 'interface', adapter_name, 'enable'],
                capture_output=True, text=True, shell=True
            )
            action = "enabled"
        
        if result.returncode == 0:
            return True, f"Ethernet 2 {action} successfully"
        else:
            return False, f"Failed to toggle adapter: {result.stderr}"
            
    except Exception as e:
        return False, f"Error toggling adapter: {e}"

def show_gui():
    """Show a simple GUI for the toggle"""
    root = tk.Tk()
    root.title("Ethernet 2 Toggle")
    root.geometry("300x150")
    root.resizable(False, False)
    
    # Check current status
    is_enabled = get_adapter_status()
    if is_enabled is None:
        status_text = "Status: Unknown"
        button_text = "Toggle Ethernet 2"
    elif is_enabled:
        status_text = "Status: Enabled"
        button_text = "Disable Ethernet 2"
    else:
        status_text = "Status: Disabled"
        button_text = "Enable Ethernet 2"
    
    # Status label
    status_label = tk.Label(root, text=status_text, font=("Arial", 12))
    status_label.pack(pady=20)
    
    def on_toggle():
        success, message = toggle_adapter()
        if success:
            messagebox.showinfo("Success", message)
            root.quit()
        else:
            messagebox.showerror("Error", message)
    
    # Toggle button
    toggle_btn = tk.Button(root, text=button_text, command=on_toggle, 
                          font=("Arial", 10), bg="#4CAF50", fg="white",
                          width=20, height=2)
    toggle_btn.pack(pady=10)
    
    # Close button
    close_btn = tk.Button(root, text="Close", command=root.quit,
                         font=("Arial", 10), width=10)
    close_btn.pack(pady=5)
    
    root.mainloop()

def create_desktop_shortcut():
    """Create a desktop shortcut for the toggle script"""
    try:
        # Get desktop path (OneDrive Desktop)
        desktop = os.path.join(os.path.expanduser("~"), "OneDrive", "Desktop")
        if not os.path.exists(desktop):
            desktop = winshell.desktop()  # Fallback to regular desktop
        
        # Shortcut details
        shortcut_name = "Ethernet 2 Toggle.lnk"
        shortcut_path = os.path.join(desktop, shortcut_name)
        script_path = os.path.abspath(__file__)
        
        # Create the shortcut
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{script_path}"'
        shortcut.WorkingDirectory = os.path.dirname(script_path)
        shortcut.IconLocation = "netshell.dll,21"  # Network icon
        shortcut.Description = "Toggle Ethernet 2 Network Adapter"
        shortcut.save()
        
        print(f"✓ Desktop shortcut created: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"✗ Error creating shortcut: {e}")
        return False

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--create-shortcut":
        # Create desktop shortcut
        create_desktop_shortcut()
    else:
        # Run the toggle GUI
        show_gui()

if __name__ == "__main__":
    main()
