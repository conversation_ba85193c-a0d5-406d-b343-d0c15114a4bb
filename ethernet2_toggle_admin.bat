@echo off
:: Check for administrator privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
    goto :run_toggle
) else (
    echo Requesting administrator privileges...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
    exit /b
)

:run_toggle
echo.
echo Ethernet 2 Network Adapter Toggle
echo =================================

:: Check current status
for /f "tokens=*" %%i in ('netsh interface show interface "Ethernet 2" ^| findstr "Admin State"') do set admin_state=%%i

echo Current status: %admin_state%
echo.

:: Check if enabled or disabled
echo %admin_state% | findstr "Enabled" >nul
if %errorlevel% == 0 (
    echo Ethernet 2 is currently ENABLED
    echo Disabling Ethernet 2...
    netsh interface set interface "Ethernet 2" disable
    if %errorlevel% == 0 (
        echo ✓ Ethernet 2 has been DISABLED
    ) else (
        echo ✗ Failed to disable Ethernet 2
    )
) else (
    echo Ethernet 2 is currently DISABLED
    echo Enabling Ethernet 2...
    netsh interface set interface "Ethernet 2" enable
    if %errorlevel% == 0 (
        echo ✓ Ethernet 2 has been ENABLED
    ) else (
        echo ✗ Failed to enable Ethernet 2
    )
)

echo.
echo Press any key to close...
pause >nul
