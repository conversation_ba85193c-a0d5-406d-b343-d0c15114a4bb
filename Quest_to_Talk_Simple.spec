# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['Warcraft/updatedQ2T_simple_alttab.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources/read_aloud_button.png', 'resources'),
    ],
    hiddenimports=[
        'customtkinter',
        'pygetwindow',
        'pynput',
        'pytesseract',
        'pyautogui',
        'pyperclip',
        'PIL',
        'PIL.Image',
        'PIL.ImageEnhance', 
        'PIL.ImageOps',
        'tkinter',
        'threading',
        'sys',
        'os',
        'time',
        're',
        'subprocess'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Quest_to_Talk_Simple',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/quest_icon_sharp.ico',
)
