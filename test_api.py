#!/usr/bin/env python3

import os
from google import genai

# Your API key
API_KEY = "AIzaSyBr2ObEMt7htXiyIsagTLZddxePjOz9gcc"

def test_gemini_api():
    """Test the Gemini API with your key"""
    print("Testing Google Gemini API...")
    print("=" * 40)
    
    # Set the API key
    os.environ['GEMINI_API_KEY'] = API_KEY
    print(f"✓ API Key set: {API_KEY[:10]}...{API_KEY[-4:]}")
    
    try:
        # Initialize client
        client = genai.Client()
        print("✓ Client initialized successfully")
        
        # Make a simple request
        print("\nMaking API request...")
        response = client.models.generate_content(
            model="gemini-2.5-flash", 
            contents="Say hello and confirm you're working!"
        )
        
        print("✓ API request successful!")
        print("\nResponse:")
        print("-" * 20)
        print(response.text)
        print("-" * 20)
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_gemini_api()
    if success:
        print("\n🎉 Your Gemini API is working perfectly!")
        print("You're using the free tier and ready to go!")
    else:
        print("\n❌ There was an issue with the API setup.")
