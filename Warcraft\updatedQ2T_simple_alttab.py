import pyautogui
import time
import pyperclip
import re
import threading
import sys
import os

# Attempt to import required libraries
try:
    import customtkinter as ctk
except ImportError:
    print("Error: CustomTkinter not found. Please install it: pip install customtkinter")
    import tkinter as tk
    ctk = tk; ctk.CTk = tk.Tk; ctk.CTkButton = tk.Button; ctk.CTkLabel = tk.Label
    def set_appearance_mode(mode): pass
    def set_default_color_theme(theme): pass
    ctk.set_appearance_mode = set_appearance_mode; ctk.set_default_color_theme = set_default_color_theme
    print("Warning: Using basic Tkinter as fallback. UI will not be themed.")

from pynput import keyboard
from PIL import Image, ImageEnhance, ImageOps

try:
    import pytesseract
except ImportError: print("Error: pytesseract not found."); sys.exit(1)
try:
    import pygetwindow as gw
except ImportError: print("Error: pygetwindow not found."); sys.exit(1)
if sys.platform == "win32":
    try:
        import win32gui, win32com.client
    except ImportError: print("Error: pywin32 not found. Please install: pip install pywin32"); sys.exit(1)

# --- Configuration ---
TESSERACT_CMD_PATH = r'tesseract'
TEXT_REGION = (1584, 341, 664, 654)
CHATBOX_COORDS = (1407, 1243)
IMAGE_CONFIDENCE = 0.7
REFERENCE_IMAGE_PATH = r"C:\Users\<USER>\OneDrive\Desktop\temp region dump\region_1021x1089_41x38.png"
FOCUS_CLICK_COORDS = (831, 1155)
GAME_WINDOW_TITLES = ["World of Warcraft", "WoW", "Warcraft"]
CHROME_WINDOW_TITLES = ["Google Chrome", "Chrome"]
EDGE_WINDOW_TITLES = ["Microsoft Edge", "Edge"]

# --- Enhanced Window Management ---
def find_window_by_titles(title_list):
    for title in title_list:
        windows = gw.getWindowsWithTitle(title)
        if windows: return windows[0], title
    return None, None

def activate_window_robust(title_list):
    """Enhanced window activation with multiple fallback methods"""
    target_window, found_title = find_window_by_titles(title_list)
    if not target_window:
        print(f"ERROR: No window found with titles: {title_list}")
        return False
    if target_window.isActive: return True

    # Method 0: Forceful Win32 API
    try:
        shell = win32com.client.Dispatch("WScript.Shell"); shell.SendKeys('%')
        win32gui.SetForegroundWindow(target_window._hWnd); time.sleep(0.5)
        active_win = gw.getActiveWindow()
        if active_win and any(t.lower() in active_win.title.lower() for t in title_list): return True
    except Exception: pass

    # Method 1: Standard Activation
    try:
        if target_window.isMinimized: target_window.restore(); time.sleep(0.3)
        target_window.activate(); time.sleep(0.5)
        if target_window.isActive: return True
    except Exception: pass
    
    print(f"✗ All activation methods failed for titles: {title_list}"); return False

# --- Helper Functions (filter_text, etc.) ---
def filter_text(raw_text):
    lines = raw_text.splitlines(); filtered_lines = []; skip_section = False; npc_name = None
    for line in lines:
        if line.startswith("[NPC:"): npc_name = re.sub(r"\[NPC:\s*\d+\]\s*", "", line); continue
        if "Objectives" in line: skip_section = True; continue
        if "Rewards" in line: skip_section = False; continue
        if skip_section: continue
        if re.match(r"\[CurrencyID:\s*\d+\]\s*.*", line): continue
        if not line.startswith("[Quest:"): filtered_lines.append(line)
    if npc_name: filtered_lines.insert(0, npc_name)
    return "\n".join(filtered_lines)
def extract_text_from_region(region):
    try:
        screenshot = pyautogui.screenshot(region=region)
        processed_image = ImageOps.autocontrast(ImageEnhance.Contrast(screenshot.convert("L")).enhance(2.0))
        return pytesseract.image_to_string(processed_image, lang='eng', config=r'--oem 3 --psm 6')
    except Exception as e: print(f"OCR Error: {e}"); return ""
def is_image_displayed(reference_image_path, confidence=IMAGE_CONFIDENCE):
    try:
        w, h = pyautogui.size(); search_region = (0, int(h * 0.2), w, int(h * 0.75))
        result = pyautogui.locate(reference_image_path, pyautogui.screenshot(region=search_region), confidence=confidence)
        if result: x, y, w_img, h_img = result; return True, (x + search_region[0] + w_img // 2, y + search_region[1] + h_img // 2)
        return False, None
    except Exception: return False, None
    
# --- Main Application Class ---
class AutomationApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Quest to Talk - Hybrid Switching")
        self.root.geometry("450x500")

        # State variables
        self.running = False; self.listener_object = None; self.pressed_keys = set()
        self.queue_hotkey_pressed = threading.Event()
        self.process_hotkey_pressed = threading.Event()
        self.shift_process_hotkey_pressed = threading.Event()
        self.queued_quests = []
        
        # Tesseract Config
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # UI Setup
        if hasattr(ctk, "set_appearance_mode"): ctk.set_appearance_mode("System"); ctk.set_default_color_theme("blue")
        self.status_label = ctk.CTkLabel(root, text="Status: Stopped", width=300); self.status_label.pack(pady=10, padx=20)
        self.queue_label = ctk.CTkLabel(root, text="Queued Quests: 0", width=300, text_color="gray"); self.queue_label.pack(pady=(0, 10), padx=20)
        self.browser_frame = ctk.CTkFrame(root); self.browser_frame.pack(pady=5, padx=20, fill="x")
        ctk.CTkLabel(self.browser_frame, text="Browser:").pack(side="left", padx=(10, 5))
        self.browser_choice = ctk.StringVar(value="Chrome")
        ctk.CTkRadioButton(self.browser_frame, text="Chrome", variable=self.browser_choice, value="Chrome").pack(side="left", padx=5)
        ctk.CTkRadioButton(self.browser_frame, text="Edge", variable=self.browser_choice, value="Edge").pack(side="left", padx=5)
        self.start_button = ctk.CTkButton(root, text="Start", command=self.start_listener, width=150); self.start_button.pack(pady=5, padx=20)
        self.stop_button = ctk.CTkButton(root, text="Stop", command=self.stop_listener, width=150, fg_color="red", hover_color="darkred"); self.stop_button.pack(pady=5, padx=20)
        self.quit_button = ctk.CTkButton(root, text="Quit", command=self.quit_app, width=150, fg_color="gray", hover_color="darkgray"); self.quit_button.pack(pady=(5, 10), padx=20)

        # Log Window
        self.log_textbox = ctk.CTkTextbox(root, height=150, activate_scrollbars=True)
        self.log_textbox.pack(pady=(0, 10), padx=20, fill="both", expand=True)
        self.log_textbox.configure(state="normal")
        sys.stdout = self.PrintLogger(self.log_textbox); sys.stderr = self.PrintLogger(self.log_textbox)
        
        self.root.protocol("WM_DELETE_WINDOW", self.quit_app)

    class PrintLogger:
        def __init__(self, textbox): self.textbox = textbox
        def write(self, text): self.textbox.after(0, self._insert_text, text)
        def _insert_text(self, text): self.textbox.insert("end", text); self.textbox.see("end")
        def flush(self): pass

    def update_status(self, text, is_running=None): self.root.after(0, self._update_ui, text, is_running)
    def _update_ui(self, text, is_running):
        if self.root.winfo_exists():
            self.status_label.configure(text=f"Status: {text}")
            if is_running is not None:
                state = "disabled" if is_running else "normal"
                self.start_button.configure(state=state)
                self.stop_button.configure(state="normal" if is_running else "disabled")
    
    def update_queue_status(self, count): self.root.after(0, lambda: self.queue_label.configure(text=f"Queued Quests: {count}"))
    
    def on_press(self, key):
        self.pressed_keys.add(key)
        try:
            if key == keyboard.Key.left: self.queue_hotkey_pressed.set()
            elif key == keyboard.Key.right:
                if keyboard.Key.shift in self.pressed_keys or keyboard.Key.shift_r in self.pressed_keys: self.shift_process_hotkey_pressed.set()
                else: self.process_hotkey_pressed.set()
        except: pass
        return True
        
    def on_release(self, key):
        try: self.pressed_keys.remove(key)
        except KeyError: pass
        if not self.running: return False
        return True

    def start_listener(self):
        if self.running: return
        self.running = True
        self.pressed_keys.clear(); self.queued_quests.clear()
        self.queue_hotkey_pressed.clear(); self.process_hotkey_pressed.clear(); self.shift_process_hotkey_pressed.clear()
        self.update_queue_status(0)
        self.update_status("Starting...", True)
        
        automation_thread = threading.Thread(target=self.automation_loop, daemon=True); automation_thread.start()
        listener_thread = threading.Thread(target=self._run_listener_thread, daemon=True); listener_thread.start()
        
        self.update_status("Running (Waiting for Hotkey)", True); print("Listener started. LEFT to queue, RIGHT to process.")

    def _run_listener_thread(self):
        try:
            with keyboard.Listener(on_press=self.on_press, on_release=self.on_release) as self.listener_object:
                self.listener_object.join()
        except Exception as e: print(f"Listener Error: {e}"); self.running = False
        finally: print("Listener thread finished."); self.listener_object = None

    def stop_listener(self):
        if not self.running: return
        print("Stopping listener and automation...")
        self.running = False
        self.queue_hotkey_pressed.set(); self.process_hotkey_pressed.set(); self.shift_process_hotkey_pressed.set()
        if self.listener_object: self.listener_object.stop()
        self.update_status("Stopped", False); print("Stopped.")

    def _handle_queue_quest(self):
        self.update_status("Queuing quest...", True)
        raw_text = extract_text_from_region(TEXT_REGION)
        if raw_text and "OCR ERROR" not in raw_text:
            self.queued_quests.append(filter_text(raw_text))
            self.update_queue_status(len(self.queued_quests))
            print(f"Quest queued. Total: {len(self.queued_quests)}")
        else:
            print("OCR failed, nothing queued.")
        self.update_status("Running (Waiting for Hotkey)", True)

    def automation_loop(self):
        while self.running:
            is_queue_action, is_process_action, shift_mode = False, False, False
            while self.running:
                if self.queue_hotkey_pressed.wait(0.1): is_queue_action = True; break
                if self.shift_process_hotkey_pressed.wait(0.1): is_process_action = True; shift_mode = True; break
                if self.process_hotkey_pressed.wait(0.1): is_process_action = True; break
            
            self.queue_hotkey_pressed.clear(); self.process_hotkey_pressed.clear(); self.shift_process_hotkey_pressed.clear()
            if not self.running: break

            try:
                if is_queue_action:
                    self._handle_queue_quest()
                    continue

                if is_process_action:
                    self.update_status("Processing...", True)
                    raw_text = extract_text_from_region(TEXT_REGION)
                    current_text = filter_text(raw_text) if raw_text and "OCR ERROR" not in raw_text else ""
                    all_texts = self.queued_quests + ([current_text] if current_text else [])
                    if not all_texts:
                        print("No text to process."); self.update_status("No text found", True); continue
                    
                    final_text_to_paste = "\n\n---\n\n".join(all_texts)
                    pyperclip.copy(final_text_to_paste)
                    self.queued_quests.clear(); self.update_queue_status(0)

                    # <<< --- HYBRID WINDOW SWITCHING LOGIC --- >>>
                    # Step 1: Break focus from the game with a generic Alt-Tab. This is crucial for sticky fullscreen windows.
                    print("Breaking game focus with Alt-Tab...")
                    pyautogui.hotkey('alt', 'tab')
                    time.sleep(0.5) # Give the OS time to process the switch.

                    # Step 2: Now that focus is broken, reliably switch to the specific target browser.
                    browser = self.browser_choice.get(); 
                    target_titles = CHROME_WINDOW_TITLES if browser == "Chrome" else EDGE_WINDOW_TITLES
                    print(f"Switching specifically to {browser}...")
                    if not activate_window_robust(target_titles):
                        self.update_status(f"Error: {browser} not found!", True); continue

                    # Continue with browser automation
                    if shift_mode: pyautogui.hotkey('ctrl', 'tab'); time.sleep(0.5)
                    pyautogui.click(CHATBOX_COORDS[0], CHATBOX_COORDS[1]); time.sleep(0.2)
                    pyautogui.hotkey('ctrl', 'v'); time.sleep(0.3)
                    pyautogui.press('enter'); time.sleep(1.0)
                    
                    pyautogui.click(FOCUS_CLICK_COORDS); time.sleep(0.2)

                    # <<< --- NEW ROBUST SEARCH AND CLICK LOGIC --- >>>
                    print("Searching for the 'Read Aloud' button...")
                    found = False
                    coords = None

                    # Loop for up to 10 seconds (20 attempts * 0.5s) to find the button
                    for i in range(20):
                        if not self.running: break # Exit if user stopped the script

                        found, coords = is_image_displayed(REFERENCE_IMAGE_PATH)
                        if found:
                            print(f"✓ Button FOUND at coordinates {coords}.")
                            break # Exit the search loop and proceed to the click logic
                        else:
                            # On the very first attempt, click to make sure the window has focus for scrolling
                            if i == 0:
                                 print(f"Clicking at {FOCUS_CLICK_COORDS} to ensure window focus for scrolling...")
                                 pyautogui.click(FOCUS_CLICK_COORDS)
                                 time.sleep(0.2)

                            print(f"Button not found. Scrolling down... (Attempt {i+1}/20)")
                            pyautogui.press('pagedown')
                            time.sleep(0.5) # Wait for page to settle after scroll

                    # If the button was found in the loop above, now try to click it.
                    if found:
                        print("Attempting to click the found button...")
                        click_successful = False
                        # Try to click up to 3 times, verifying each time.
                        for attempt in range(3):
                            # Re-locate the image to get fresh coordinates before clicking
                            can_still_see, click_coords = is_image_displayed(REFERENCE_IMAGE_PATH)

                            if can_still_see:
                                print(f"Click attempt {attempt + 1}: Clicking at {click_coords}...")
                                pyautogui.click(click_coords)
                                pyautogui.moveTo(500, 500, duration=0.2) # Move mouse away to not interfere with next check
                                time.sleep(0.5) # Wait for UI to update

                                # Verify if the click worked (the button should be gone)
                                if not is_image_displayed(REFERENCE_IMAGE_PATH)[0]:
                                    print("✓ Button click successful (it disappeared).")
                                    click_successful = True
                                    break # Exit the click-retry loop
                                else:
                                    print(f"✗ Click attempt {attempt + 1} failed, button is still visible.")
                            else:
                                # If the button is already gone, our job is done.
                                print("Button disappeared before we could click. Assuming success.")
                                click_successful = True
                                break # Exit the click-retry loop

                        if not click_successful:
                             print("✗ Failed to confirm button click after 3 attempts.")
                    else:
                        print("✗ Button was not found after 10 seconds of searching and scrolling.")

                    if shift_mode: pyautogui.hotkey('ctrl', 'shift', 'tab'); time.sleep(0.5)
                    
                    # Step 3: Reliably switch back to the game.
                    print("Switching back to game...")
                    if not activate_window_robust(GAME_WINDOW_TITLES):
                        self.update_status("Error: Game not found!", True)
                    
                    print("Cycle complete."); self.update_status("Running (Waiting for Hotkey)", True)

            except Exception as e:
                print(f"!!! ERROR during automation sequence: {e}")
                self.update_status(f"Error occurred", True)
                # Attempt to switch back to game on error
                activate_window_robust(GAME_WINDOW_TITLES)

        print("Automation loop finished.")

    def quit_app(self):
        print("Quit requested."); self.stop_listener(); time.sleep(0.2)
        print("Destroying window."); self.root.destroy()

if __name__ == "__main__":
    if sys.platform == "win32":
        try: from ctypes import windll; windll.shcore.SetProcessDpiAwareness(1)
        except: pass
    root = ctk.CTk()
    app = AutomationApp(root)
    root.mainloop()
    print("Application exited.")