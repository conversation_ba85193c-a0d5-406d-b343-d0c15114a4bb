' Ethernet 2 Toggle Script (VBScript - No Admin Prompts)
' This script toggles Ethernet 2 using WMI without requiring admin privileges

Dim objWMIService, colNetAdapters, objNetAdapter
Dim strAdapterName, strMessage, intResult

' Set the adapter name
strAdapterName = "Ethernet 2"

' Connect to WMI
Set objWMIService = GetObject("winmgmts:\\.\root\cimv2")

' Get the network adapter
Set colNetAdapters = objWMIService.ExecQuery("SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionID = '" & strAdapterName & "'")

If colNetAdapters.Count = 0 Then
    MsgBox "Network adapter '" & strAdapterName & "' not found!", vbCritical, "Error"
    WScript.Quit
End If

' Get the first (and should be only) adapter
For Each objNetAdapter In colNetAdapters
    ' Check current status and toggle
    If objNetAdapter.NetEnabled = True Then
        ' Disable the adapter
        intResult = objNetAdapter.Disable()
        If intResult = 0 Then
            strMessage = "Ethernet 2 has been DISABLED"
            MsgBox strMessage, vbInformation, "Network Toggle"
        Else
            strMessage = "Failed to disable Ethernet 2 (Error: " & intResult & ")"
            MsgBox strMessage, vbCritical, "Error"
        End If
    Else
        ' Enable the adapter
        intResult = objNetAdapter.Enable()
        If intResult = 0 Then
            strMessage = "Ethernet 2 has been ENABLED"
            MsgBox strMessage, vbInformation, "Network Toggle"
        Else
            strMessage = "Failed to enable Ethernet 2 (Error: " & intResult & ")"
            MsgBox strMessage, vbCritical, "Error"
        End If
    End If
    Exit For
Next

' Clean up
Set objNetAdapter = Nothing
Set colNetAdapters = Nothing
Set objWMIService = Nothing
