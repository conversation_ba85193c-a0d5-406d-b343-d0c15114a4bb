#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a desktop shortcut for Network Connections
(Control Panel\Network and Internet\Network Connections)
"""

import os
import winshell
from win32com.client import Dispatch

def create_network_connections_shortcut():
    """Create a desktop shortcut for Network Connections"""
    
    # Get desktop path
    desktop = winshell.desktop()
    
    # Shortcut details
    shortcut_name = "Network Connections.lnk"
    shortcut_path = os.path.join(desktop, shortcut_name)
    
    # Target for Network Connections
    # This opens the Network Connections control panel directly
    target = "ncpa.cpl"
    
    try:
        # Create the shortcut
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = "rundll32.exe"
        shortcut.Arguments = "shell32.dll,Control_RunDLL ncpa.cpl"
        shortcut.WorkingDirectory = ""
        shortcut.IconLocation = "ncpa.cpl,0"
        shortcut.Description = "Network Connections - Control Panel"
        shortcut.save()
        
        print(f"✓ Desktop shortcut created successfully!")
        print(f"  Location: {shortcut_path}")
        print(f"  Target: Network Connections Control Panel")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating shortcut: {e}")
        print("\nAlternative method - Manual creation:")
        print("1. Right-click on desktop")
        print("2. New > Shortcut")
        print("3. Enter: rundll32.exe shell32.dll,Control_RunDLL ncpa.cpl")
        print("4. Name it: Network Connections")
        return False

def install_dependencies():
    """Install required dependencies if not present"""
    try:
        import winshell
        import win32com.client
        print("✓ All dependencies are available")
        return True
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Installing required packages...")
        
        import subprocess
        import sys
        
        packages = ["pywin32", "winshell"]
        for package in packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✓ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"✗ Failed to install {package}")
                return False
        
        print("Please restart the script after installation.")
        return False

if __name__ == "__main__":
    print("Creating Desktop Shortcut for Network Connections")
    print("=" * 50)
    
    # Check and install dependencies
    if install_dependencies():
        # Create the shortcut
        create_network_connections_shortcut()
    
    input("\nPress Enter to exit...")
