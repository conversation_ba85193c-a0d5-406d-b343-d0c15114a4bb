@echo off
echo Creating Network Connections desktop shortcut...

:: Create a VBS script to create the shortcut
echo Set WshShell = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo Set oShellLink = WshShell.CreateShortcut("%USERPROFILE%\OneDrive\Desktop\Network Connections.lnk") >> temp_shortcut.vbs
echo oShellLink.TargetPath = "rundll32.exe" >> temp_shortcut.vbs
echo oShellLink.Arguments = "shell32.dll,Control_RunDLL ncpa.cpl" >> temp_shortcut.vbs
echo oShellLink.Description = "Network Connections - Control Panel" >> temp_shortcut.vbs
echo oShellLink.IconLocation = "ncpa.cpl,0" >> temp_shortcut.vbs
echo oShellLink.Save >> temp_shortcut.vbs

:: Run the VBS script
cscript //nologo temp_shortcut.vbs

:: Clean up
del temp_shortcut.vbs

echo.
echo Desktop shortcut for Network Connections created successfully!
echo You can now access Network Connections directly from your desktop.
echo.
pause
