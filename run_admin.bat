@echo off
echo Running Quest to Talk with Administrator privileges...
echo.

cd /d "%~dp0"

echo Current directory: %CD%
echo.

echo Checking if Python is available...
python --version
if errorlevel 1 (
    echo ERROR: Python not found in PATH
    echo Please ensure Python is installed and added to PATH
    pause
    exit /b 1
)

echo.
echo Checking if script exists...
if not exist "Warcraft\updatedQ2T gemini.py" (
    echo ERROR: Script not found at "Warcraft\updatedQ2T gemini.py"
    echo Current files in Warcraft folder:
    dir "Warcraft\*.py" /b
    pause
    exit /b 1
)

echo.
echo Starting Quest to Talk...
echo If no GUI appears, check for error messages below:
echo.

python "Warcraft\updatedQ2T gemini.py"

echo.
echo Script finished. Exit code: %ERRORLEVEL%
if not %ERRORLEVEL%==0 (
    echo There was an error running the script.
)

pause
