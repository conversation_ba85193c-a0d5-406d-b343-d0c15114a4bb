#!/usr/bin/env python3
"""
Creates a PDF user manual for Quest to Talk
"""

try:
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.platypus.tableofcontents import TableOfContents
except ImportError:
    print("Installing reportlab for PDF generation...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors

def create_user_manual():
    """Create the PDF user manual"""
    
    # Create PDF document
    doc = SimpleDocTemplate("Quest_to_Talk_User_Manual.pdf", pagesize=letter)
    story = []
    styles = getSampleStyleSheet()
    
    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        textColor=colors.darkblue,
        alignment=1  # Center
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        textColor=colors.darkblue
    )
    
    # Title Page
    story.append(Paragraph("Quest to Talk", title_style))
    story.append(Paragraph("Multi-Quest Voice Automation", styles['Heading2']))
    story.append(Spacer(1, 0.5*inch))
    story.append(Paragraph("User Manual", styles['Heading2']))
    story.append(Spacer(1, 1*inch))
    story.append(Paragraph("Simple Setup and Usage Guide", styles['Normal']))
    story.append(PageBreak())
    
    # Table of Contents
    story.append(Paragraph("Table of Contents", heading_style))
    story.append(Paragraph("1. What is Quest to Talk?", styles['Normal']))
    story.append(Paragraph("2. Before You Start (Requirements)", styles['Normal']))
    story.append(Paragraph("3. First Time Setup", styles['Normal']))
    story.append(Paragraph("4. How to Use the Program", styles['Normal']))
    story.append(Paragraph("5. Key Combinations", styles['Normal']))
    story.append(Paragraph("6. Troubleshooting", styles['Normal']))
    story.append(Paragraph("7. Tips and Tricks", styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 1: What is Quest to Talk?
    story.append(Paragraph("1. What is Quest to Talk?", heading_style))
    story.append(Paragraph("""
    Quest to Talk is a program that helps you play World of Warcraft by automatically reading quest text out loud using AI voice technology.
    
    <b>What it does:</b>
    • Takes screenshots of quest text in your game
    • Converts the text using OCR (Optical Character Recognition)
    • Sends the text to an AI voice service
    • Plays the quest text as speech
    
    <b>Why use it?</b>
    • Enjoy quests without reading walls of text
    • Multitask while questing
    • Better immersion with voice acting
    • Accessibility for players who prefer audio
    """, styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 2: Requirements
    story.append(Paragraph("2. Before You Start (Requirements)", heading_style))
    story.append(Paragraph("""
    <b>REQUIRED SOFTWARE (Must install these first!):</b>
    
    1. <b>Tesseract OCR</b> - This reads text from screenshots
       • Download from: https://github.com/UB-Mannheim/tesseract/wiki
       • Choose "tesseract-ocr-w64-setup-5.x.x.exe"
       • Install with default settings
       • IMPORTANT: Check "Add to PATH" during installation
    
    2. <b>World of Warcraft</b> - Obviously needed!
       • Game should be in windowed or borderless windowed mode
       • Resolution should be 1920x1080 or higher
    
    3. <b>AI Voice Service</b> - Choose one:
       • ChatGPT with voice (recommended)
       • Google Gemini with voice
       • Any AI chat service that can read text aloud
    
    <b>RECOMMENDED SETUP:</b>
    • Dual monitor setup (game on one, AI chat on other)
    • Good internet connection
    • Headphones or speakers
    """, styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 3: First Time Setup
    story.append(Paragraph("3. First Time Setup", heading_style))
    story.append(Paragraph("""
    <b>Step 1: Install Tesseract OCR</b>
    1. Download Tesseract from the link in Chapter 2
    2. Run the installer
    3. IMPORTANT: Check "Add to PATH" option
    4. Click through with default settings
    5. Restart your computer after installation
    
    <b>Step 2: Set Up Your AI Voice Service</b>
    1. Open your preferred AI service (ChatGPT, Gemini, etc.)
    2. Make sure voice/speech features are enabled
    3. Test that it can read text aloud
    4. Keep this window open and ready
    
    <b>Step 3: Configure World of Warcraft</b>
    1. Set game to Windowed or Borderless Windowed mode
    2. Use 1920x1080 resolution or higher
    3. Make sure quest text is clearly visible
    4. Position game window where you want it
    
    <b>Step 4: Test the Program</b>
    1. Run Quest to Talk
    2. Click "Start" button
    3. Open a quest in WoW
    4. Press AltGr key to test
    5. Check that text appears in your AI chat
    """, styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 4: How to Use
    story.append(Paragraph("4. How to Use the Program", heading_style))
    story.append(Paragraph("""
    <b>Basic Usage (Single Quest):</b>
    1. Start the program and click "Start"
    2. Open a quest in World of Warcraft
    3. Press the <b>AltGr</b> key (bottom right of keyboard)
    4. The program will:
       • Take a screenshot of the quest
       • Extract the text
       • Switch to your AI chat
       • Paste the text
       • Click the "read aloud" button
       • Switch back to the game
    
    <b>Multiple Quest Mode:</b>
    1. For the first quest: Press <b>Ctrl + #</b> to store it
    2. For the second quest: Press <b>Ctrl + #</b> to store it
    3. Continue storing quests as needed
    4. When ready: Press <b>AltGr</b> to process all stored quests at once
    
    <b>Voice Tab Switching:</b>
    • Press <b>Shift + AltGr</b> if you need to switch voice tabs in your AI service
    
    <b>Program Controls:</b>
    • <b>Start</b>: Begin listening for key presses
    • <b>Stop</b>: Stop the program temporarily
    • <b>Instructions</b>: Open this manual
    • <b>Quit</b>: Close the program completely
    """, styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 5: Key Combinations
    story.append(Paragraph("5. Key Combinations (SIMPLE REFERENCE)", heading_style))
    story.append(Paragraph("""
    <font size="14"><b>REMEMBER THESE 3 KEYS:</b></font>
    
    <b>🔑 Ctrl + # (Hash key)</b>
    • STORES the current quest for later
    • Use this to collect multiple quests
    • You'll see "Quest stored! (X total)" message
    
    <b>🔑 AltGr (alone)</b>
    • PROCESSES the current quest
    • If you stored quests, it combines them all
    • This is your main "GO" button
    
    <b>🔑 Shift + AltGr</b>
    • Same as AltGr but switches voice tabs
    • Use if your AI has multiple voice options
    
    <font size="12"><b>TYPICAL WORKFLOW:</b></font>
    1. See quest #1 → Press Ctrl + #
    2. See quest #2 → Press Ctrl + #  
    3. See quest #3 → Press AltGr (processes all 3)
    
    <b>OR for single quests:</b>
    1. See quest → Press AltGr (processes immediately)
    """, styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 6: Troubleshooting
    story.append(Paragraph("6. Troubleshooting", heading_style))
    story.append(Paragraph("""
    <b>Problem: "Tesseract not found" error</b>
    • Solution: Reinstall Tesseract and check "Add to PATH"
    • Restart your computer after installation
    
    <b>Problem: No text is captured</b>
    • Check that WoW is in windowed mode
    • Make sure quest text is visible and clear
    • Try adjusting your screen resolution
    
    <b>Problem: Wrong text is captured</b>
    • Make sure only the quest dialog is visible
    • Close other windows that might interfere
    • Check that quest text is not overlapped by UI elements
    
    <b>Problem: Program doesn't respond to key presses</b>
    • Make sure the program is "Started" (green status)
    • Try clicking in the game window first
    • Check that no other programs are blocking key input
    
    <b>Problem: AI chat doesn't receive text</b>
    • Make sure your AI chat window is open
    • Check that the chat input box is clickable
    • Try manually clicking in the chat box first
    
    <b>Problem: "Read aloud" button not found</b>
    • Make sure your AI service supports voice/speech
    • Check that the voice feature is enabled
    • Try manually clicking the read button to see where it is
    """, styles['Normal']))
    story.append(PageBreak())
    
    # Chapter 7: Tips and Tricks
    story.append(Paragraph("7. Tips and Tricks", heading_style))
    story.append(Paragraph("""
    <b>🎯 Best Practices:</b>
    • Use dual monitors if possible (game + AI chat)
    • Keep quest dialogs clean and unobstructed
    • Store multiple related quests for better context
    • Test with simple quests first
    
    <b>🚀 Pro Tips:</b>
    • You can store up to 5 quests at once
    • Stored quests are cleared after processing
    • The program shows how many quests are stored
    • Use Ctrl+# for quest chains that are related
    
    <b>⚙️ Customization:</b>
    • Screen coordinates can be adjusted in the code
    • OCR confidence can be tuned for better accuracy
    • Different AI services may need different settings
    
    <b>🎮 Gaming Tips:</b>
    • Works great for story-heavy zones
    • Perfect for leveling alts
    • Great for players who want to multitask
    • Excellent for accessibility needs
    
    <b>🔧 Performance:</b>
    • Close unnecessary programs for better OCR
    • Good lighting improves text recognition
    • Higher resolution = better text capture
    • Stable internet = smoother AI responses
    """, styles['Normal']))
    
    # Build PDF
    doc.build(story)
    print("✓ User manual created: Quest_to_Talk_User_Manual.pdf")

if __name__ == "__main__":
    create_user_manual()
