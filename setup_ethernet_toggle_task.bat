@echo off
echo Setting up Ethernet 2 Toggle without admin prompts...
echo.

:: Check for administrator privileges (needed for initial setup only)
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This setup requires administrator privileges (one time only).
    echo After setup, the toggle will work without admin prompts.
    echo.
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
    exit /b
)

echo Running with administrator privileges for setup...
echo.

:: Create the toggle script
echo @echo off > ethernet2_toggle_task.bat
echo :: Toggle Ethernet 2 without admin prompts >> ethernet2_toggle_task.bat
echo. >> ethernet2_toggle_task.bat
echo :: Check current status >> ethernet2_toggle_task.bat
echo for /f "tokens=*" %%%%i in ('netsh interface show interface "Ethernet 2" ^^^| findstr "Admin State"') do set admin_state=%%%%i >> ethernet2_toggle_task.bat
echo. >> ethernet2_toggle_task.bat
echo :: Toggle based on current state >> ethernet2_toggle_task.bat
echo echo %%%%admin_state%%%% ^^^| findstr "Enabled" ^^^>nul >> ethernet2_toggle_task.bat
echo if %%%%errorlevel%%%% == 0 ( >> ethernet2_toggle_task.bat
echo     netsh interface set interface "Ethernet 2" disable >> ethernet2_toggle_task.bat
echo     echo Ethernet 2 DISABLED >> ethernet2_toggle_task.bat
echo ) else ( >> ethernet2_toggle_task.bat
echo     netsh interface set interface "Ethernet 2" enable >> ethernet2_toggle_task.bat
echo     echo Ethernet 2 ENABLED >> ethernet2_toggle_task.bat
echo ) >> ethernet2_toggle_task.bat

:: Create the scheduled task (runs with highest privileges, no prompt)
echo Creating scheduled task...
schtasks /create /tn "Ethernet2Toggle" /tr "%CD%\ethernet2_toggle_task.bat" /sc ondemand /ru "SYSTEM" /f >nul 2>&1

if %errorlevel% == 0 (
    echo ✓ Scheduled task created successfully
) else (
    echo ✗ Failed to create scheduled task
    pause
    exit /b
)

:: Create desktop shortcut that triggers the task
echo Creating desktop shortcut...

echo Set WshShell = WScript.CreateObject("WScript.Shell") > temp_task_shortcut.vbs
echo Set oShellLink = WshShell.CreateShortcut("%USERPROFILE%\OneDrive\Desktop\Ethernet 2 Toggle.lnk") >> temp_task_shortcut.vbs
echo oShellLink.TargetPath = "schtasks.exe" >> temp_task_shortcut.vbs
echo oShellLink.Arguments = "/run /tn ""Ethernet2Toggle""" >> temp_task_shortcut.vbs
echo oShellLink.Description = "Toggle Ethernet 2 (No Admin Prompt)" >> temp_task_shortcut.vbs
echo oShellLink.IconLocation = "netshell.dll,21" >> temp_task_shortcut.vbs
echo oShellLink.Save >> temp_task_shortcut.vbs

cscript //nologo temp_task_shortcut.vbs
del temp_task_shortcut.vbs

echo ✓ Desktop shortcut created
echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Your "Ethernet 2 Toggle" desktop shortcut is ready!
echo.
echo Features:
echo ✓ No admin prompts when using
echo ✓ One-click toggle
echo ✓ Runs silently in background
echo.
echo To remove this setup later, run:
echo schtasks /delete /tn "Ethernet2Toggle" /f
echo.
pause
