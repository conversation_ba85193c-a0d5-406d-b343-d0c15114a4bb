# Quest to Talk - Simple Alt+Tab Version

A World of Warcraft automation tool that reads quest text aloud using browser-based text-to-speech.

## 🎯 **What This Does**

- **Extracts quest text** from WoW using OCR (Optical Character Recognition)
- **Switches to browser** using reliable Alt+Tab method
- **Pastes text into ChatGPT/AI chat** for text-to-speech conversion
- **Clicks "Read Aloud" button** automatically
- **Returns to game** seamlessly

## 🚀 **Quick Start**

### **Option 1: Use the EXE (Recommended)**
1. **Download**: `dist/Quest_to_Talk_Simple.exe`
2. **Install Tesseract**: Run `tesseract-installer.exe` (included)
3. **Run**: Double-click the EXE file
4. **Setup**: Make sure your browser has ChatGPT or similar AI chat open

### **Option 2: Run from Source**
1. **Install Python 3.8+**
2. **Install requirements**: `pip install -r requirements.txt`
3. **Run**: `python Warcraft/updatedQ2T_simple_alttab.py`

## 🎮 **How to Use**

### **Setup:**
1. **Open WoW** in any display mode (fullscreen, windowed, etc.)
2. **Open browser** with ChatGPT or AI chat ready
3. **Start the app** and click "Start"

### **Controls:**
- **Ctrl + #**: Store current quest for later (up to 5 quests)
- **AltGr**: Process current quest (+ any stored quests)
- **Shift + AltGr**: Process with voice tab switching

### **The Magic:**
1. **Alt+Tab breaks out** of fullscreen automatically
2. **Finds your browser** and switches to it
3. **Pastes quest text** into the chat
4. **Clicks "Read Aloud"** button
5. **Returns to WoW** ready for more

## 🛠️ **Features**

### **✅ Reliable Window Switching**
- **Works with fullscreen WoW** - No need to change display modes
- **Simple Alt+Tab method** - Always works, no complex detection
- **Automatic browser detection** - Finds Chrome/Edge automatically

### **📝 Smart Quest Management**
- **Multi-quest support** - Store up to 5 quests and process together
- **Text filtering** - Removes UI elements, keeps only quest content
- **OCR preprocessing** - Enhanced image processing for better text recognition

### **🎵 Audio Features**
- **Voice tab switching** - Switch between different AI voices
- **Robust button clicking** - Multiple attempts with verification
- **Real-time feedback** - See exactly what's happening in the log

## 📁 **Project Structure**

```
Warcraft-Automation/
├── dist/
│   └── Quest_to_Talk_Simple.exe    # Ready-to-run executable
├── Warcraft/
│   └── updatedQ2T_simple_alttab.py # Main source code
├── resources/
│   └── read_aloud_button.png       # Button image for detection
├── tesseract-installer.exe         # OCR engine installer
├── requirements.txt                # Python dependencies
└── README.md                       # This file
```

## 🔧 **Requirements**

### **For EXE Version:**
- **Windows 10/11**
- **Tesseract OCR** (installer included)
- **Browser** with AI chat (ChatGPT, Claude, etc.)

### **For Source Version:**
- **Python 3.8+**
- **Dependencies**: `pip install -r requirements.txt`
- **Tesseract OCR** (installer included)

## 🎯 **Why This Version?**

### **Simple & Reliable:**
- ✅ **No complex window detection** - just uses Alt+Tab
- ✅ **Works with ANY display mode** - fullscreen, windowed, borderless
- ✅ **No "Error code from Windows: 0" issues**
- ✅ **Much more reliable** than forced window activation

### **User-Friendly:**
- ✅ **One-click EXE** - no Python installation needed
- ✅ **Real-time logging** - see exactly what's happening
- ✅ **Test functionality** - verify everything works before using
- ✅ **Clean interface** - simple and intuitive

## 🐛 **Troubleshooting**

### **Common Issues:**
1. **"Tesseract not found"** - Run the included `tesseract-installer.exe`
2. **"Button not found"** - Make sure ChatGPT is open and visible
3. **"OCR failed"** - Check that quest text is clearly visible in WoW
4. **Window switching issues** - Make sure browser was the last active window before WoW

### **Tips:**
- **Test first** - Use the "Test Alt+Tab" button to verify switching works
- **Browser setup** - Have ChatGPT open and ready before starting
- **Quest positioning** - Make sure quest text is in the default UI position
- **Multiple monitors** - Works best when WoW and browser are on the same monitor

## 📝 **Version History**

- **v3.0 (Current)** - Simple Alt+Tab version with robust button clicking
- **v2.x** - Complex window detection versions (removed)
- **v1.x** - Original proof of concept (removed)

## 🎉 **Enjoy!**

This tool makes questing much more immersive by having quest text read aloud. Perfect for:
- **Accessibility** - Visual impairment support
- **Multitasking** - Listen while doing other things
- **Immersion** - Feel more connected to the story
- **Convenience** - No need to read walls of text

Happy questing! 🏰⚔️
