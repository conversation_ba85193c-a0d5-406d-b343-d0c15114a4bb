@echo off
echo ========================================
echo Building Quest to Talk Simple EXE
echo ========================================
echo.

echo Checking if PyInstaller is installed...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo PyInstaller not found. Installing...
    pip install pyinstaller
    if errorlevel 1 (
        echo Failed to install PyInstaller!
        pause
        exit /b 1
    )
) else (
    echo PyInstaller is already installed.
)

echo.
echo Building EXE with PyInstaller...
pyinstaller Quest_to_Talk_Simple.spec --clean --noconfirm

if errorlevel 1 (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo EXE created at: dist\Quest_to_Talk_Simple.exe
    echo.
    echo You can now run the EXE directly without Python!
    echo.
)

pause
