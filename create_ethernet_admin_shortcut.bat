@echo off
echo Creating Ethernet 2 Toggle (Admin) desktop shortcut...

:: Create a VBS script to create the shortcut
echo Set WshShell = WScript.CreateObject("WScript.Shell") > temp_admin_shortcut.vbs
echo Set oShellLink = WshShell.CreateShortcut("%USERPROFILE%\OneDrive\Desktop\Ethernet 2 Toggle.lnk") >> temp_admin_shortcut.vbs
echo oShellLink.TargetPath = "%CD%\ethernet2_toggle_admin.bat" >> temp_admin_shortcut.vbs
echo oShellLink.WorkingDirectory = "%CD%" >> temp_admin_shortcut.vbs
echo oShellLink.Description = "Toggle Ethernet 2 Network Adapter (Auto Admin)" >> temp_admin_shortcut.vbs
echo oShellLink.IconLocation = "netshell.dll,21" >> temp_admin_shortcut.vbs
echo oShellLink.Save >> temp_admin_shortcut.vbs

:: Run the VBS script
cscript //nologo temp_admin_shortcut.vbs

:: Clean up
del temp_admin_shortcut.vbs

echo.
echo ✓ Desktop shortcut "Ethernet 2 Toggle" created successfully!
echo.
echo Features:
echo - One-click toggle for Ethernet 2
echo - Automatically requests admin privileges
echo - Shows current status before toggling
echo - Simple command-line interface
echo.
echo Just double-click the desktop icon to use it!
echo.
pause
