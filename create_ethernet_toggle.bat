@echo off
echo Creating Ethernet 2 Toggle desktop shortcut...

:: Create a VBS script to create the shortcut
echo Set WshShell = WScript.CreateObject("WScript.Shell") > temp_ethernet_shortcut.vbs
echo Set oShellLink = WshShell.CreateShortcut("%USERPROFILE%\OneDrive\Desktop\Ethernet 2 Toggle.lnk") >> temp_ethernet_shortcut.vbs
echo oShellLink.TargetPath = "python.exe" >> temp_ethernet_shortcut.vbs
echo oShellLink.Arguments = """%CD%\ethernet2_toggle.py""" >> temp_ethernet_shortcut.vbs
echo oShellLink.WorkingDirectory = "%CD%" >> temp_ethernet_shortcut.vbs
echo oShellLink.Description = "Toggle Ethernet 2 Network Adapter On/Off" >> temp_ethernet_shortcut.vbs
echo oShellLink.IconLocation = "netshell.dll,21" >> temp_ethernet_shortcut.vbs
echo oShellLink.Save >> temp_ethernet_shortcut.vbs

:: Run the VBS script
cscript //nologo temp_ethernet_shortcut.vbs

:: Clean up
del temp_ethernet_shortcut.vbs

echo.
echo ✓ Desktop shortcut "Ethernet 2 Toggle" created successfully!
echo.
echo The shortcut will:
echo - Show current status of Ethernet 2 (Enabled/Disabled)
echo - Allow you to toggle it on/off with one click
echo - Require administrator privileges to work
echo.
echo Right-click the shortcut and select "Run as administrator" for it to work properly.
echo.
pause
